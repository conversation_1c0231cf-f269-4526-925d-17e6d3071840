<script lang="ts" setup>
import { ref, watch } from 'vue'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/Icon.vue'
import { commonEmojis } from '@/utils'

const props = defineProps<{
  existEmoji: string,     // 现有的emoji
}>()

const emit = defineEmits(['emojiChange'])

// 内部状态，使用 props.existEmoji 作为初始值
const emoji = ref<string>(props.existEmoji)
const open = ref(false)

// 当 existEmoji 变化时更新内部状态
watch(() => props.existEmoji, (newEmoji) => {
  emoji.value = newEmoji
})

// 当emoji内部值变化时通知父组件
watch(emoji, (newEmoji) => {
  emit('emojiChange', newEmoji)
})

// 处理emoji点击
const handleEmojiClick = (selectedEmoji: string) => {
  emoji.value = selectedEmoji
  open.value = false
}
</script>

<template>
  <Popover v-model:open="open">
    <PopoverTrigger as-child>
      <Button 
        variant="outline" 
        class="justify-center"
        type="button"
      >
        <span v-if="emoji" class="text-base">{{ emoji }}</span>
        <Icon v-else name="radix-icons:face" class="w-4 h-4" />
      </Button>
    </PopoverTrigger>
    <PopoverContent 
      class="w-[300px] p-0" 
      @open-auto-focus.prevent
    >
      <div class="flex flex-wrap p-2 gap-2">
        <Input
          placeholder="输入emoji"
          v-model="emoji"
        />
        <Button
          v-for="commonEmoji in commonEmojis"
          :key="commonEmoji"
          variant="ghost"
          size="sm"
          class="text-xl h-10 w-10 p-0 rounded-full hover:bg-accent"
          @click.stop="handleEmojiClick(commonEmoji)"
        >
          {{ commonEmoji }}
        </Button>
      </div>
    </PopoverContent>
  </Popover>
</template> 