<script lang="ts" setup>
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuItem, DropdownMenuGroup } from  '@/components/ui/dropdown-menu'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import Icon from '@/components/Icon.vue';

const emit = defineEmits(['logout', 'open-manage'])

const { clear: clearUserSession, refreshSession, currentUser: user } = useUserSession()
async function logOut() {
  await clearUserSession()
}

const website = import.meta.env.VITE_WEBSITE
async function handleLogin() {
  browser.tabs.create({ url: website, active: true });
}

async function handleLogout() {
  await clearUserSession() 
  emit('logout')
}

async function runSync() {
  refreshSession()
}
</script>

<template>
  <DropdownMenu v-if="user">
    <DropdownMenuTrigger as-child>
      <Button
        variant="ghost"
        class="flex h-11 items-center gap-2 px-1 data-[state=open]:bg-accent data-[state=open]:text-accent-foreground"
      >
        <!-- <Avatar class="size-8 rounded-lg">
          <AvatarImage
            :src="user.avatarUrl || ''"
            :alt="user.name"
          />
          <AvatarFallback class="rounded-lg">
            {{ user.name || user.email }}
          </AvatarFallback>
        </Avatar> -->
        <div class="grid flex-1 text-left text-sm leading-tight">
          <!-- <span class="truncate font-semibold">{{ user.name }}</span> -->
          <span class="truncate text-xs text-muted-foreground">{{ user.email }}</span>
        </div>
        <Icon name="radix-icons:caret-sort" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent
      class="w-[--radix-dropdown-menu-trigger-width] min-w-56"
      side="bottom"
      align="center"
      :side-offset="4"
    >
      <DropdownMenuLabel class="p-0 font-normal">
        <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
          <Avatar class="size-8 rounded-lg">
            <AvatarImage
              :src="user.avatarUrl || ''"
              :alt="user.name"
            />
            <AvatarFallback class="rounded-lg">
              {{ user.name || user.email }}
            </AvatarFallback>
          </Avatar>
          <div class="grid flex-1 text-left text-sm leading-tight">
            <span class="truncate font-semibold">{{ user.name }}</span>
            <span class="truncate text-xs">{{ user.email }}</span>
          </div>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem as-child>
        <a href="/">
          <Icon
            name="lucide:slash"
            class="mr-2"
          /> Home
        </a>
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem as-child>
          <a href="/pricing">
            <span class="mr-2 size-4">✨</span> Upgrade to Pro
          </a>
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem as-child>
          <a href="/dashboard/settings">
            Account
          </a>
        </DropdownMenuItem>
        <DropdownMenuItem as-child>
          <a href="/dashboard/settings/billing">
            Billing
          </a>
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuItem @click="clearUserSession">
        <Icon
          name="lucide:log-out"
          class="mr-2"
        />
        Log out
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
  <div v-else>
    <Button
        as-child
        variant="ghost"
        class="rounded-full"
        size="lg"
      >
        <a
          class="font-semibold text-muted-foreground hover:text-foreground"
          :href="`${website}/auth/login-with-password`"
          target="_blank"
        >
          {{ i18n.t('options.header.signIn') }}
        </a>
      </Button>
  </div>
</template> 