/**
 * 封装 RxDB 操作，通过消息通信与 background 交互
 */
export function useRxDBMessage() {
  // const find = async (collection: string, query: any) => {
  //   return await chrome.runtime.sendMessage({
  //     action: 'find',
  //     collection,
  //     query,
  //     timestamp: Date.now()
  //   });
  // };

  const rxdb = {
    // 查询多个文档
    find: async <T>(collection: string, query: any = {}): Promise<T[]> => {
      const result = await chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:find',
        collection,
        query,
      });
      return result;
    },

    // 查询单个文档
    findOne: async <T>(collection: string, query: any = {}): Promise<T | null> => {
      const result = await chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:findOne',
        collection,
        query,
      });
      return result;
    },

    // 插入文档
    insert: async <T>(collection: string, data: any): Promise<T> => {
      const result = await chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:insert',
        collection,
        data,
      });
      return result;
    },

    // 需要条件更新、增量修改（如 $inc）或部分字段更新时用 update
    update: async <T>(collection: string, query: any, data: any): Promise<T> => {
      const result = await chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:update',
        collection,
        query,
        data,
      });
      return result;
    },

    // 需要简单覆盖字段时用 patch
    patch: async <T>(collection: string, query: any): Promise<T> => {
      const result = await chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:patch',
        collection,
        query
      });
      return result;
    },

    // 如果记录存在则更新，不存在则创建
    upsert: async <T>(collection: string, query: any, data: any): Promise<T> => {
      const result = await chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:upsert',
        collection,
        query,
        data,
      });
      return result;
    },

    // 删除文档
    remove: async (collection: string, query: any): Promise<void> => {
      const result = await chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:remove',
        collection,
        query,
      });
      return result;
    },

    // 计数文档
    count: async (collection: string, query: any = {}): Promise<number> => {
      const result = await chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:count',
        collection,
        query,
      });
      return result;
    },

    // 订阅实时查询
    subscribeLiveQuery: (params: {
      key: string;
      collection: string;
      query: any;
      onChange: (results: any[]) => void;
    }) => {
      const id = params.key;
      const listener = (msg: any) => {
        if (msg.action === 'liveQueryUpdate' && msg.id === id) {
          params.onChange(msg.results);
        }
      };

      chrome.runtime.onMessage.addListener(listener);
      chrome.runtime.sendMessage({
        type: 'rxdb',
        action: 'rxdb:subscribeLiveQuery',
        collection: params.collection,
        query: params.query,
        id,
      });

      return () => {
        chrome.runtime.onMessage.removeListener(listener);
        chrome.runtime.sendMessage({
          type: 'rxdb',
          action: 'rxdb:unsubscribeLiveQuery',
          id,
        });
      };
    },
  }

  return rxdb;
}
