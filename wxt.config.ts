import { defineConfig } from 'wxt';
import removeConsole from 'vite-plugin-remove-console';
import tailwindcss from "@tailwindcss/vite";

// See https://wxt.dev/api/config.html
export default defineConfig({
  dev: {
    server: {
      port: 7000,
    },
  },
  modules: ['@wxt-dev/module-vue', '@wxt-dev/i18n/module', '@wxt-dev/analytics/module'],
  manifest: {
    name: "__MSG_name__",
    short_name: "__MSG_short_name__",
    description: "__MSG_description__",
    permissions: ["favicon", "alarms", "bookmarks", "storage", "identity", "identity.email", "contextMenus", "tabs", "sidePanel"],
    host_permissions: [
      "https://gochat.pro/*"
    ],
    // externally_connectable: {
    //   "matches": ["https://gochat.pro/*"]
    // },
    default_locale: 'en',
    options_ui: {
      page: 'options.html',
      open_in_tab: true,
    },
    // "background": {
    //   "service_worker": "background.js",
    //   "persistent": true  // 确保为 true
    // },
    action: {
      "default_title": "Click to open panel",
      "default_icon": {
        "16": "/icon/<EMAIL>",
        "32": "/icon/<EMAIL>",
        "48": "/icon/<EMAIL>",
        "64": "/icon/<EMAIL>",
        "128": "/icon/<EMAIL>"
      },
      "default_popup": "popup.html"
    },
    homepage_url: 'https://gochat.pro',
    side_panel: {
      "default_path": "side-panel.html",
      "default_icon": {
        "16": "/icon/16.png",
        "32": "/icon/32.png",
        "48": "/icon/48.png",
        "64": "/icon/64.png",
        "128": "/icon/128.png"
      }
    },
    icons: {
      "16": "/icon/16.png",
      "32": "/icon/32.png",
      "48": "/icon/48.png",
      "64": "/icon/64.png",
      "128": "/icon/128.png"
    }
  },
  vite: (configEnv) => {
    return {
      // 调试生产环境
      // build: {
      //   sourcemap: true,
      //   minify: false,
      //   rollupOptions: {
      //     output: {
      //       manualChunks: undefined // 避免过度代码分割
      //     }
      //   }
      // },
      // esbuild: {
      //   drop: configEnv.mode === 'production' ? ['debugger'] : [],
      //   keepNames: true // 保留函数名和类名，便于调试
      // },
      plugins: [
        tailwindcss(),
        configEnv.mode === 'production'
          ? [removeConsole({ includes: ['log', 'warn'] })]
          : []
      ],
    };
  }
});
