<script lang="ts" setup>
import { Button } from '@/components/ui/button'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
} from '@/components/ui/alert-dialog'

import AutosizeTextarea from '@/components/AutosizeTextarea.vue'

const props = defineProps<{
  open: boolean,
  editedBookmark: {
    id: string | null,
    title: string,
    url: string,
    parentId: string
  }
}>()

const emit = defineEmits(['openChange', 'update'])

// 创建本地副本
const localBookmark = ref({...props.editedBookmark})

// 监听属性变化，更新本地副本
watch(() => props.editedBookmark, (newValue) => {
  localBookmark.value = {...newValue}
}, { deep: true })

function handleSave() {
  // 将修改后的数据发送到父组件
  emit('update', {
    bookmark: localBookmark.value
  })
  emit('openChange', false)
}

function handleCancel() {
  // 关闭对话框，不保存更改
  emit('openChange', false)
}
</script>

<template>
  <AlertDialog :open="open" @update:open="(val: boolean) => emit('openChange', val)" >
    <AlertDialogContent
      class="max-w-[calc(100vw-8rem)]"
      @pointer-down-outside="() => emit('openChange', false)"
    >
      <AlertDialogHeader>
        <AlertDialogTitle></AlertDialogTitle>
        <AlertDialogDescription></AlertDialogDescription>
      </AlertDialogHeader>
      
      <div class="flex flex-col gap-4">
        <div class="space-y-2">
          <AutosizeTextarea 
            v-model="localBookmark.title"
            class="bg-background placeholder:text-muted-foreground/70 text-sm w-full"
            :placeholder="i18n.t('form.title.placeholder')"
            :disableNewlines="true"
          />
        </div>

        <div class="space-y-2">
          <AutosizeTextarea 
            v-model="localBookmark.url"
            class="bg-background placeholder:text-muted-foreground/70 text-sm w-full"
            :placeholder="i18n.t('form.url.placeholder')"
            :disableNewlines="true"
          />
        </div>
      </div>
      
      <AlertDialogFooter class="flex flex-row justify-end">
        <Button type="button" @click="handleSave">
          {{ i18n.t('dialog.action.done') }}
        </Button>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template> 