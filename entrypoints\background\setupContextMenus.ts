export async function setupContextMenus() {  
  // 创建页面右键菜单，多个页面右键菜单将自动合并为子菜单 - 改为不合并
  chrome.runtime.onInstalled.addListener(() => {
    chrome.contextMenus.create({
      id: 'gochat_page_menu',
      title: chrome.i18n.getMessage("page_menu_title"),
      contexts: ['page']
    });

    chrome.contextMenus.create({
      id: 'gochat_create_bookmark',
      parentId: 'gochat_page_menu',  // 指定父级菜单
      title: chrome.i18n.getMessage("page_menu_create_bookmark"),
      contexts: ['page']
    });

    // // 创建第二个子菜单
    browser.contextMenus.create({
      id: 'gochat_open_sidepanel',
      parentId: 'gochat_page_menu',
      title: chrome.i18n.getMessage("page_menu_open_manager"),
      contexts: ['page']
    });
  });
  
  // 监听右键菜单点击事件
  chrome.contextMenus.onClicked.addListener(async function (info, tab: any) {
    if (info.menuItemId === "gochat_open_sidepanel") {
      try {
        // 使用 chrome.sidePanel.open() API 打开 side panel
        await chrome.sidePanel.open({ tabId: tab.id });
      } catch (error) {
        console.error('Failed to open side panel:', error);
        // 如果 sidePanel API 失败，回退到在新标签页中打开
        const fullUrl = browser.runtime.getURL(`/side-panel.html`);
        browser.tabs.query({ url: fullUrl }, (tabs) => {
          if (tabs.length > 0 && tabs[0].id) {
            browser.tabs.update(tabs[0].id, { active: true });
          } else {
            browser.tabs.create({ url: fullUrl, active: true });
          }
        });
      }
    } else if (info.menuItemId === "gochat_create_bookmark") {
      browser.action.openPopup();
    }
  });
}