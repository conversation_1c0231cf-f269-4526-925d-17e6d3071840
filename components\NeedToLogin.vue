<script setup lang="ts">
import { Dialog, DialogTitle, DialogContent, DialogHeader, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'

const website = import.meta.env.VITE_WEBSITE

const props = defineProps<{
  open: boolean
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
}>()

async function handleLogin() {
  emit('openChange', false)
  browser.tabs.create({ url: `${website}/auth/login-with-password`, active: true });
}
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>Need to login</DialogTitle>
        <DialogDescription>
          Please login to continue.
        </DialogDescription>
      </DialogHeader>

      <DialogFooter>
        <Button
          @click="handleLogin"
        >
          Login
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
