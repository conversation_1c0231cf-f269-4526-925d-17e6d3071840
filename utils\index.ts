// 通用函数，用于提取 url 中的 domain
export function getDomain(url: string) {
  const parsedUrl = new URL(url)
  return parsedUrl.hostname.replace(/^www\./, '')
}

// 预设常用 emoji 列表
export const commonEmojis = ['🚩','🚀', '⚡', '😀', '🔥', '🏆', '🎉', '⭐', '❤️', '💡', '😈', '💩'] 

export function faviconURL(u: string) {
  const url = new URL(browser.runtime.getURL('/_favicon/' as any));
  url.searchParams.set('pageUrl', u);
  url.searchParams.set('size', '32');
  return url.toString();
}

/**
 * 计算字符串的哈希值 (简单哈希，快速但碰撞风险较高)
 * @param str 需要计算哈希的字符串
 * @returns 哈希字符串 (8字符)
 */
export function hashString(str: string): string {
  // 使用简单的哈希算法计算哈希值
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }

  // 转换为16进制字符串，确保结果为正数
  return (hash >>> 0).toString(16);
}
