// 根据当前标签页是否存在书签，设置 action icon
export const setActionIcon = async () => {
  const [tab] = await browser.tabs.query({
    active: true,
    currentWindow: true,
  });

  if (!tab) return;

  const url = tab.url || tab.pendingUrl;

  const nodes = await browser.bookmarks.search({
    url,
  });

  if (nodes.length > 0) {
    browser.action.setIcon({
      path: {
        16: "/icon/<EMAIL>",
        32: "/icon/<EMAIL>",
        48: "/icon/<EMAIL>",
        64: "/icon/<EMAIL>",
        128: "/icon/<EMAIL>"
      },
    })
  } else {
    browser.action.setIcon({
      path: {
        16: "/icon/<EMAIL>",
        32: "/icon/<EMAIL>",
        48: "/icon/<EMAIL>",
        64: "/icon/<EMAIL>",
        128: "/icon/<EMAIL>"
      },
    })
  }
};

export async function setupActionIcon() {  
  // watching the active tab change
  browser.tabs.onActivated.addListener((activeInfo) => {
    setActionIcon();
  });

  // watching the window focus change
  browser.windows.onFocusChanged.addListener((windowId) => {
    setActionIcon();
  });

  // watching tab (url) update
  browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (!changeInfo.url) return;
    setActionIcon();
  });

  // 在书签或文件夹发生变化时触发。注意：目前，只有标题和网址更改会触发此操作
  browser.bookmarks.onChanged.addListener((id, changeInfo) => {
    setActionIcon();
  });

  // 在创建书签或文件夹时触发。
  browser.bookmarks.onCreated.addListener((id, bookmarkNode) => {
    setActionIcon();
  });

  // 在移除书签或文件夹时触发。递归移除文件夹时，系统会为该文件夹触发一次通知，而不会为其内容触发任何通知。
  browser.bookmarks.onRemoved.addListener((id, removeInfo) => {
    setActionIcon();
  });
}