<script lang="ts" setup>
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import TagsSelector from '@/components/TagsSelector.vue'
import Icon from '@/components/Icon.vue'

const props = defineProps<{
  open: boolean,
  selectedTags: string[],
  allTags: string[],
  editedExtra: {
    note: string,
    emoji: string
  },
}>()

const emit = defineEmits(['openChange', 'update'])

// 创建本地副本
const localExtra = ref({...props.editedExtra})
const localTags = ref<string[]>([...props.selectedTags])

// 监听属性变化，更新本地副本
watch(() => props.editedExtra, (newValue) => {
  localExtra.value = {...newValue}
}, { deep: true })

watch(() => props.selectedTags, (newValue) => {
  localTags.value = [...newValue]
})

function handleTagsChange(tags: string[]) {
  localTags.value = tags
}

function handleSave() {
  // 将修改后的数据发送到父组件
  emit('update', {
    extra: localExtra.value,
    tags: localTags.value
  })
  emit('openChange', false)
}

function handleCancel() {
  // 关闭对话框，不保存更改
  emit('openChange', false)
}
</script>

<template>
  <AlertDialog :open="open" @update:open="(val: boolean) => emit('openChange', val)">
    <AlertDialogContent
      class="max-w-[calc(100vw-8rem)]"
      @pointer-down-outside="() => emit('openChange', false)"
    >
      <AlertDialogHeader>
        <AlertDialogTitle></AlertDialogTitle>
        <AlertDialogDescription></AlertDialogDescription>
      </AlertDialogHeader>
      
      <div class="flex flex-col gap-4">
        <TagsSelector 
          :exist-tags="selectedTags"
          :all-tags="allTags"
          :selected-tags="localTags"
          @tags-change="handleTagsChange"
        />

        <Textarea
        class="bg-background placeholder:text-muted-foreground/70 text-sm w-full"
        :placeholder="i18n.t('form.note.placeholder')"
        v-model="localExtra.note"
        />
      </div>
      
      <AlertDialogFooter class="flex justify-between">
        <Button type="button" variant="outline" @click="handleCancel">
          {{ i18n.t('dialog.action.cancel') }}
        </Button>
        <Button type="button" @click="handleSave">
          {{ i18n.t('dialog.action.done') }}
        </Button>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template> 