import { initRxDB, rxDBInstance} from '@/rxdb';
import { Subscription } from 'rxjs';

export function setupRxDB() {
  // 存储实时查询的订阅信息
  const liveQueries = new Map<string, Subscription>();
  
  // 实际的 rxdb 操作，通过 chrome.runtime.onMessage 监听数据库操作请求，并返回结果
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type !== 'rxdb') {
      return;
    }

    console.warn('RxDB onMessage - ', request);

    // 确保数据库已初始化
    (async () => {
      if (!rxDBInstance) {
        await initRxDB();
      }
    })();

    const { action, collection, query = {}, data, id } = request;
    const collectionInstance = rxDBInstance.collections[collection];

    if (!collectionInstance) {
      throw new Error(`集合 ${collection} 不存在`);
    }

    // 取消旧订阅（如果存在）
    if (liveQueries.has(request.id)) {
      console.warn('setupRxDB - unsubscribe - ', request.id)
      liveQueries.get(request.id)?.unsubscribe();
    }
    
    // async 放在 addListener 内部
    // 因为 chrome.runtime.onMessage 的监听器函数如果是 async，会导致消息端口在函数执行完（但可能未真正完成异步操作）时立即关闭，导致前端收到 undefined。
    (async () => {
      try {
        let result;
        switch (action) {
          case 'rxdb:find':
            result = (await collectionInstance.find(query).exec()).map(doc => doc.toJSON());
            break;
          case 'rxdb:findOne':
            const doc = await collectionInstance.findOne(query).exec();
            result = doc ? doc.toJSON() : null;
            break;
          case 'rxdb:insert':
            result = (await collectionInstance.insert(data)).toJSON();
            break;
          case 'rxdb:update':
            const docToUpdate = await collectionInstance.findOne(query).exec();
            if (!docToUpdate) throw new Error('not found');
            await docToUpdate.update({ $set: data });
            result = docToUpdate.toJSON();
            break;
          case 'rxdb:patch':
            const docToPatch = await collectionInstance.findOne(query.selector).exec();
            if (!docToPatch) throw new Error('not found');
            await docToPatch.patch(query.data);
            result = docToPatch.toJSON();
            break;
          case 'rxdb:upsert':
            const docToUpsert = await collectionInstance.findOne(query).exec();
            await docToUpsert.upsert(data);
            result = docToUpsert.toJSON();
            break;
          case 'rxdb:remove':
            const docToRemove = await collectionInstance.findOne(query).exec();
            if (!docToRemove) throw new Error('未找到匹配的文档');
            await docToRemove.remove();
            result = undefined; // 删除操作无需返回数据
            break;
          case 'rxdb:count':
            result = (await collectionInstance.find(query).exec()).length;
            break;
          case 'rxdb:subscribeLiveQuery':
            const subscription = collectionInstance
              .find(query)
              .$.subscribe((results) => {
                console.warn('liveQueryUpdate - sended!', results)
                chrome.runtime.sendMessage({
                  action: 'liveQueryUpdate',
                  id,
                  results: results.map(doc => doc.toJSON()),
                });
              });
            liveQueries.set(id, subscription);
            result = { success: true };
            break;
          case 'rxdb:unsubscribeLiveQuery':
            const subscriptionToRemove = liveQueries.get(id);
            if (subscriptionToRemove) {
              subscriptionToRemove.unsubscribe();
              liveQueries.delete(id);
            }
            result = { success: true };
            break;
          default:
            throw new Error(`不支持的操作: ${action}`);
        }
        sendResponse(result);
      } catch (error: any) {
        sendResponse({ 
          success: false, 
          error: error,
        });
      }
    })();

    return true; // 保持长连接
  });
}