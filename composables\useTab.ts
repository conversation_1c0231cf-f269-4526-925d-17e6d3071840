export default function useTab() {
  // get current active tab
  const getActiveTab = async () => {
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });
    return tab;
  };

  // create new tab
  const createNewTab = async (active = true) => {
    const newTab = await chrome.tabs.create({
      active,
    });
    return newTab;
  };

  // open bookmark on current tab or new tab
  const openBookmark = (nodeId: string, mode = 'new', active = true): Promise<chrome.tabs.Tab> => new Promise((resolve, reject) => {
    chrome.bookmarks.get(nodeId)
      .then(async (nodes) => {
        const { url } = nodes[0];
        let tab!: chrome.tabs.Tab;
        if (mode === 'new') {
          tab = await createNewTab(active);
        } else {
          tab = await getActiveTab();
        }
        
        await chrome.tabs.update(tab.id!, {
          url,
        });
        resolve(tab);
      });
  });

  // get all tab groups
  const getAllTabGroups = async () => {
    const tabGroups = await chrome.tabGroups.query({
      windowId: chrome.windows.WINDOW_ID_CURRENT,
    });
    return tabGroups;
  };

  const watchTabGroups = (callback: (group: chrome.tabGroups.TabGroup) => void) => {
    chrome.tabGroups.onCreated.addListener(callback);
    chrome.tabGroups.onRemoved.addListener(callback);
    chrome.tabGroups.onUpdated.addListener(callback);
  };

  // create a group with tab
  const createTabInGroup = async (tabId: number, group: chrome.tabGroups.TabGroup) => {
    const groupId = await chrome.tabs.group({
      tabIds: tabId,
    });

    await chrome.tabGroups.update(groupId, {
      color: group.color,
      title: group.title,
    });

    return groupId;
  };

  // open bookmark in a group
  const openBookmarkOnGroup = async (nodeId: string, groupId: number) => {
    const tab = await openBookmark(nodeId, 'new', false);
    await chrome.tabs.group({
      groupId,
      tabIds: tab.id,
    });
    return tab;
  };

  return {
    getActiveTab,
    createNewTab,
    openBookmark,
    getAllTabGroups,
    watchTabGroups,
    createTabInGroup,
    openBookmarkOnGroup,
  };
}
