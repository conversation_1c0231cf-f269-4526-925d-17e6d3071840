@import 'tailwindcss';
/*
  ---break---
*/
@custom-variant dark (&:is(.dark *));

/* shadcn v4 默认不提供 cursor: pointer  */
button,[role=button] {
  cursor: pointer
}

.router-link-active, .router-link-exact-active {
  @apply bg-muted;
}

@theme {
  --font-display:
    Segoe UI, Tahoma, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-sans:
    Segoe UI, Tahoma, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;

  --radius-xl: calc(var(--radius) + 4px);
  --radius-lg: calc(var(--radius) + 2px);
  --radius: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@variant dark (&:where(.dark, .dark *));

@layer base {
  * {
    @apply border-border;
  }

  html {
    color-scheme: light dark;
  }

  html.dark {
    color-scheme: dark;
  }

  html.light {
    color-scheme: light;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(240 10% 3.9%);

  --muted: hsl(240 4.8% 95.9%);
  --muted-foreground: hsl(240 3.8% 46.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(240 10% 3.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(240 10% 3.9%);

  --border: hsl(240 5.9% 90%);
  --input: hsl(240 5.9% 90%);

  --primary: hsl(155.3deg 78.4% 40%);
  --primary-foreground: hsl(0 0% 98%);

  --secondary: hsl(240 4.8% 95.9%);
  --secondary-foreground: hsl(240 5.9% 10%);

  --accent: hsl(240 4.8% 95.9%);
  --accent-foreground: hsl(240 5.9% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: 155.3deg 78.4% 40%;

  --radius: 0.5rem;

  --sidebar:
    hsl(0 0% 98%);

  --sidebar-foreground:
    hsl(240 5.3% 26.1%);

  --sidebar-primary:
    hsl(240 5.9% 10%);

  --sidebar-primary-foreground:
    hsl(0 0% 98%);

  --sidebar-accent:
    hsl(240 4.8% 95.9%);

  --sidebar-accent-foreground:
    hsl(240 5.9% 10%);

  --sidebar-border:
    hsl(220 13% 91%);

  --sidebar-ring:
    hsl(217.2 91.2% 59.8%);
}

.dark {
  --background: hsl(240 10% 3.9%);
  --foreground: hsl(0 0% 98%);

  --muted: hsl(240 3.7% 15.9%);
  --muted-foreground: hsl(240 5% 64.9%);

  --popover: hsl(240 10% 3.9%);
  --popover-foreground: hsl(0 0% 98%);

  --card: hsl(240 10% 3.9%);
  --card-foreground: hsl(0 0% 98%);

  --border: hsl(240 3.7% 15.9%);
  --input: hsl(240 3.7% 15.9%);

  --primary: hsl(154.9 100% 19.2%);
  --primary-foreground: hsl(355.7 100% 97.3%);

  --secondary: hsl(240 3.7% 15.9%);
  --secondary-foreground: hsl(0 0% 98%);

  --accent: hsl(240 3.7% 15.9%);
  --accent-foreground: hsl(0 0% 98%);

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;

  --ring: 154.9 100% 19.2%;

  --sidebar:
    hsl(240 5.9% 10%);

  --sidebar-foreground:
    hsl(240 4.8% 95.9%);

  --sidebar-primary:
    hsl(224.3 76.3% 48%);

  --sidebar-primary-foreground:
    hsl(0 0% 100%);

  --sidebar-accent:
    hsl(240 3.7% 15.9%);

  --sidebar-accent-foreground:
    hsl(240 4.8% 95.9%);

  --sidebar-border:
    hsl(240 3.7% 15.9%);

  --sidebar-ring:
    hsl(217.2 91.2% 59.8%);
}

/*
  ---break---
*/

@theme inline {
  --color-sidebar:
    var(--sidebar);
  --color-sidebar-foreground:
    var(--sidebar-foreground);
  --color-sidebar-primary:
    var(--sidebar-primary);
  --color-sidebar-primary-foreground:
    var(--sidebar-primary-foreground);
  --color-sidebar-accent:
    var(--sidebar-accent);
  --color-sidebar-accent-foreground:
    var(--sidebar-accent-foreground);
  --color-sidebar-border:
    var(--sidebar-border);
  --color-sidebar-ring:
    var(--sidebar-ring);
}

/*
  ---break---
*/

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}