import { ref } from 'vue';

// 定义对话框状态类型（包含参数）
interface DialogState {
  isOpen: boolean;
  props?: Record<string, any>; // 动态参数
}

const dialogs = ref<Record<string, DialogState>>({});

export function useDialog() {
  const registerDialog = (name: string) => {
    dialogs.value[name] = { isOpen: false, props: {} };
  };

  const openDialog = (name: string, props?: Record<string, any>) => {
    if (name in dialogs.value) dialogs.value[name] = { isOpen: true, props };
  };

  const closeDialog = (name: string) => {
    if (name in dialogs.value) dialogs.value[name] = { isOpen: false, props: {} };
  };

  const toggleDialog = (name: string) => {
    if (name in dialogs.value) dialogs.value[name] = { isOpen: !dialogs.value[name].isOpen, props: dialogs.value[name].props };
  };

  const closeAllDialogs = () => {
    Object.keys(dialogs.value).forEach(name => {
      dialogs.value[name] = { isOpen: false, props: {} };
    });
  };

  return { dialogs, registerDialog, openDialog, closeDialog, toggleDialog, closeAllDialogs };
}