<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import {
  Dialog,
  DialogContent,
  DialogOverlay,
  DialogTitle,
  DialogHeader,
  DialogDescription,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { watch } from 'vue'
import Icon from '@/components/Icon.vue'

const props = defineProps<{
  open: boolean
  currentFolderId: string
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    title: z.string().min(1),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
  initialValues: {
    title: 'New Folder'
  }
})

watch(() => props.open, (isOpen) => {
  if (isOpen) {
    form.resetForm({
      values: {
        title: 'New Folder'
      }
    })
  } else {
    form.resetForm()
  }
})

const onSubmit = form.handleSubmit(async (values) => {
  try {
    const updatedFolder = await browser.bookmarks.create({
      parentId: props.currentFolderId,
      title: values.title
    })
    emit('saved', updatedFolder)
    emit('openChange', false)
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(i18n.t('error.unknown'))
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >

      <DialogContent
        class="fixed left-1/2 top-1/2 z-50 grid w-64 max-w-lg -translate-x-1/2 -translate-y-1/2 gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded sm:rounded-lg"
      >
        <DialogTitle><Icon name="lucide:folder" class="w-4 h-4" /></DialogTitle>
        <DialogDescription></DialogDescription>
        <form
          id="editForm"
          class="flex flex-col items-stretch gap-4"
          :validation-schema="itemSchema"
          @submit="onSubmit"
        >
          <FormField
            v-slot="{ componentField }"
            name="title"
          >
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="i18n.t('form.folder.placeholder')"
                  v-bind="componentField"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        </form>

        <Button
          type="submit"
          form="editForm"
        >
          {{ i18n.t('dialog.action.save') }}
        </Button>
      </DialogContent>
  </Dialog>
</template>
