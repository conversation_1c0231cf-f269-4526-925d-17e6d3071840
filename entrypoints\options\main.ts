import { createApp } from 'vue';
import "~/assets/tailwind.css";
import App from './App.vue';
import initRxDB from '@/rxdb';

// const app = createApp(App);

// (async () => {
//   const rxDBInstance = await initRxDB();
//   app.provide('db', rxDBInstance);
//   app.mount('#app');
// })();

const app = createApp(App);

// 与 background.ts 是隔离的上下文环境，需单独初始化数据库，rxdb 支持多实例 
// 并且每次 popup 打开时，都会重新初始化数据库
initRxDB()
  .then(db => {
    // 数据库就绪后注入并挂载
    app.provide('db', db);
    app.mount('#app');
  })
  .catch(error => {
    console.error('Database initialization failed:', error);
    // 可添加错误提示UI
    app.mount('#app'); // 即使失败也挂载以显示错误界面
  });