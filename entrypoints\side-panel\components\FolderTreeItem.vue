<script setup lang="ts">
import { computed, h, nextTick, ref, render, watchEffect } from 'vue'
import { type FlattenedItem, TreeItem } from 'reka-ui'
import { draggable, dropTargetForElements, monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { type Instruction, type ItemMode, attachInstruction, extractInstruction } from '@atlaskit/pragmatic-drag-and-drop-hitbox/tree-item'
import { pointerOutsideOfPreview } from '@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview'
import { setCustomNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import { Icon } from '@iconify/vue'
import { unrefElement } from '@vueuse/core'

const props = defineProps<{
  item: FlattenedItem<any>
  currentFolderId: string
}>()

const elRef = ref()
const isDragging = ref(false)
const isDraggedOver = ref(false)
const isInitialExpanded = ref(false)
const instruction = ref<Extract<Instruction, { type: 'reorder-above' | 'reorder-below' | 'make-child' }> | null>(null)

const mode = computed(() => {
  if (props.item.hasChildren)
    return 'expanded'
  if (props.item.index + 1 === props.item.parentItem?.children?.length)
    return 'last-in-group'
  return 'standard'
})

watchEffect((onCleanup) => {
  const currentElement = unrefElement(elRef)

  if (!currentElement)
    return

  const item = { ...props.item.value, level: props.item.level, id: props.item._id }

  const expandItem = () => {
    if (!elRef.value?.isExpanded) {
      elRef.value?.handleToggle()
    }
  }

  const closeItem = () => {
    if (elRef.value?.isExpanded) {
      elRef.value?.handleToggle()
    }
  }

  const dndFunction = combine(
    draggable({
      element: currentElement,
      getInitialData: () => item,
      onDragStart: () => {
        isDragging.value = true
        isInitialExpanded.value = elRef.value?.isExpanded
        closeItem()
      },
      onDrop: () => {
        isDragging.value = false
        if (isInitialExpanded.value)
          expandItem()
      },
      onGenerateDragPreview({ nativeSetDragImage }) {
        setCustomNativeDragPreview({
          getOffset: pointerOutsideOfPreview({ x: '16px', y: '8px' }),
          render: ({ container }) => {
            return render(h(
              'div',
              { class: 'border border-4 border-blue-500 bg-blue-400 text-white rounded-md text-sm font-semibold px-3 py-1.5' },
              item.title,
            ), container)
          },
          nativeSetDragImage,
        })
      },
    }),

    dropTargetForElements({
      element: currentElement,
      getData: ({ input, element }) => {
        const data = { id: item.id, index: item.index, parentId: item.parentId, title: item.title }

        return attachInstruction(data, {
          input,
          element,
          indentPerLevel: 16,
          currentLevel: props.item.level,
          mode: mode.value,
          block: [],
        })
      },
      canDrop: ({ source }) => {
        return source.data.id !== item.id
      },
      onDrag: ({ self }) => {
        instruction.value = extractInstruction(self.data) as typeof instruction.value
      },
      onDragEnter: ({ source }) => {
        if (source.data.id !== item.id) {
          isDraggedOver.value = true
          expandItem()
        }
      },
      onDragLeave: () => {
        isDraggedOver.value = false
        instruction.value = null
      },
      onDrop: ({ location }) => {
        isDraggedOver.value = false
        instruction.value = null
        if (location.current.dropTargets[0].data.id === item.id) {
          nextTick(() => {
            expandItem()
          })
        }
      },
      getIsSticky: () => true,
    }),

    monitorForElements({
      canMonitor: ({ source }) => {
        return source.data.id !== item.id
      },
    }),
  )

  // Cleanup dnd function
  onCleanup(() => dndFunction())
})
</script>
<template>
  <TreeItem ref="elRef"
    v-slot="{ isExpanded , handleToggle, handleSelect}"
    :value="item.value"
    :level="item.level"
    class="relative w-full hover:bg-muted z-0"
    :class="{
      'opacity-50': isDragging, 'bg-primary/20 dark:bg-primary/50 font-semibold' : currentFolderId === item.value.id
    }"
  >
    <!-- 添加一个包装容器来处理双击事件 -->
    <div class="flex items-center w-full" @dblclick.stop="handleToggle">
      <div v-for="level in item.level" :key="level" class="w-2 min-w-2 h-full border-l ml-3 z-0" :class="{'hidden' : level === 1}"></div>
      <div
        v-if="item.value.children.length > 0"
        class="hover:bg-gray-300/50 py-1 px-1 rounded-full"
        @click.stop="handleToggle" >
        <Icon
          icon="radix-icons:triangle-right"
          class="h-4 w-4 transition"
          :class="{ 'rotate-90': isExpanded }"
        />
      </div>
      <div v-else class="h-4 w-6"></div>
      <div class="flex pl-0 items-center" >
        <Icon icon="lucide:folder" class="size-3.5 transition" />
        <span class="pl-2 truncate">{{ item.value.title }}</span>
      </div>
    </div>
    <!-- 拖拽到两行中间时下面那行的样式 -->
    <div v-if="instruction" class="absolute h-full w-full top-0 border-primary -z-2" :style="{
      left: `${instruction?.currentLevel * instruction?.indentPerLevel}px`,
      width: `calc(100% - ${instruction?.currentLevel * instruction?.indentPerLevel}px)`,
    }" :class="{
      'border-b-2!': instruction?.type === 'reorder-below',
      'border-t-2!': instruction?.type === 'reorder-above',
      'bg-primary/20!': instruction?.type === 'make-child',
    }" />
  </TreeItem>
</template>
