import { googleAnalytics4 } from '@wxt-dev/analytics/providers/google-analytics-4';

// See https://wxt.dev/api/config.html
export default defineAppConfig({
  analytics: {
    // https://wxt.dev/analytics.html
    // 默认分析功能是禁用的，并且默认用户每一个操作都会发送请求到 Google Analytics 去，这会有点问题
    // 需要开启 analytics.setEnabled(true) 后，才会发送请求到 Google Analytics 去
    enabled: storage.defineItem('local:analytics-enabled', {
      fallback: true,
    }),
    debug: false, // 发送到 https://www.google-analytics.com/debug/mp/collect，数据流页面仍然可能显示"您的网站未启用数据收集功能"
    providers: [
      googleAnalytics4({
        apiSecret: import.meta.env.WXT_GA_API_SECRET,
        measurementId: 'G-W32K0M6LKE',
      }),
    ],
  },
});
