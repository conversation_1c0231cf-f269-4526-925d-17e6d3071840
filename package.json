{"name": "gochat-ext", "description": "manifest.json description", "private": true, "version": "0.9.10", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build --analyze", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "vue-tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@vueuse/core": "^12.2.0", "@wxt-dev/analytics": "^0.5.0", "@wxt-dev/i18n": "^0.2.3", "cuid": "^3.0.0", "date-fns": "4.1.0", "lucide-vue-next": "^0.487.0", "nuxt-auth-utils": "0.4.4", "reka-ui": "^2.2.0", "rxdb": "16.13.0", "rxjs": "^7.8.1", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "vue": "3.5.13", "vue-router": "4.5.0", "vue-sonner": "^1.3.0", "zod": "3.24.1"}, "devDependencies": {"@iconify/vue": "4.2.0", "@radix-icons/vue": "^1.0.0", "@tailwindcss/vite": "^4.1.3", "@types/chrome": "^0.0.280", "@wxt-dev/module-vue": "1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "iconify-icon": "^2.2.0", "tailwind-merge": "^3.2.0", "typescript": "5.6.3", "vite-plugin-remove-console": "^2.2.0", "vue-tsc": "2.2.10", "wxt": "0.20.7"}}