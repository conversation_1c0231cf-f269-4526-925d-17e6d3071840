<script lang="ts" setup>
import { useColorMode } from '@vueuse/core'
import { TooltipProvider } from  '@/components/ui/tooltip'
import Icon from '@/components/Icon.vue';
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { initRxDB } from '@/rxdb/index'

import UserMenu from './components/UserMenu.vue'
import { Toaster } from '@/components/ui/sonner'

const browserTab = ref<chrome.tabs.Tab | null>(null)

const colorMode = useColorMode({
  storageKey: 'side-panel-vueuse-color-scheme',  // 显式指定存储键名
});
// 从 chrome.storage.sync 中获取 sidePanelcolorMode
browser.storage.sync.get(['side-panel-color-mode'], (result) => { 
  colorMode.value = result['side-panel-color-mode'] || 'light'; // 默认值为 'dark'
});
// 监听 chrome.storage.sync 中 colorMode 的变化
browser.storage.onChanged.addListener((changes) => {
  if (changes['side-panel-color-mode']) {  // 修改键名
    colorMode.value = changes['side-panel-color-mode'].newValue; 
  }
});
const toggleColorMode = () => {
  const newColorMode = colorMode.value === 'light' ? 'dark' : 'light';
  browser.storage.sync.set({ 'side-panel-color-mode': newColorMode }); // 修改键名
};


async function test() {
  const cookie = await chrome.cookies.get({ name: "nuxt-session", url: "http://localhost:3000" });
  console.log(cookie);
}

// 消息处理函数/监听书签变化
function handleRuntimeMessage(
  request: any
) {
  console.warn('--------- popup App 收到消息 -----------!', request);
  if (request.type === 'bookmark-change') {

  }
  if (request.type === 'closeSidePanel') {
    window.close();
  }
}

// 监听标签页更新事件，切换标签页不会触发
browser.tabs.onUpdated.addListener(async function(tabId, changeInfo, tab) {
  // changeInfo 是每个属性变化了都会触发一次，比如 url, faviconUrl, 并且只传变化了的那个属性，f5 时 changeInfo.url 不会触发！
  console.warn('--------- 标签页 onUpdated ---------', tabId, changeInfo, changeInfo.url, tab.url);
  if (changeInfo.url || tab.url !== browserTab.value?.url) {
    // 如果这是当前激活的标签页
    const [activeTab] = await browser.tabs.query({active: true, currentWindow: true});
    if (activeTab.id === tabId) {

    }
  }
});

// 监听标签页激活事件，切换标签页时会触发
browser.tabs.onActivated.addListener(async function(activeInfo) {
  console.warn('---------tabs.onActivated---------');
  // 获取新激活的标签页信息
  console.warn('--------- 标签页 onActivated ---------', activeInfo);
  const tab = await browser.tabs.get(activeInfo.tabId);
  if (tab.url !== browserTab.value?.url) {
  }
});

onMounted(async () => {
  // await initRxDB();
  browser.runtime.onMessage.addListener(handleRuntimeMessage);
})

onBeforeUnmount(() => {
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
})
</script>

<template>
  <TooltipProvider :delay-duration="0">
    <Toaster :duration="1000" />
    <div id="sidepanel-container" class="bg-background w-[520px]">
      <div id="sidepanel-header" class="flex justify-between bg-secondary p-1">
        <UserMenu />
        <div class="flex items-center text-muted-foreground">
          <Button size="icon" variant="ghost">
            <Icon name="lucide:home"></Icon>
          </Button>
          <Button size="icon" variant="ghost" @click="toggleColorMode">
            <Icon :name="colorMode === 'light' ? 'lucide:moon' : 'lucide:sun'" />
          </Button>

          <!-- <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button
                aria-haspopup="true"
                size="sm"
                variant="ghost"
              >
                <Icon name="radix-icons:dots-horizontal" />
                <span class="sr-only">Toggle menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem @click="toggleColorMode">
                Light / Dark
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu> -->
        </div>
      </div>
      <div id="sidepanel-content" class="">
        side panel
      </div>
    </div>
  </TooltipProvider>
</template>
