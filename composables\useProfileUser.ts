export interface ProfileUserComposable {
  /**
   * The user object if logged in, null otherwise.
   */
  currentProfileUser: ComputedRef<User | null>
}

// 模块级单例模式
let isInitialized = false
const _currentProfileUser = ref<any | null>(null)

// 初始化逻辑（只会执行一次）
const initializeProfileUser = () => {
  if (isInitialized) return
  
  // 读取初始值
  chrome.identity.getProfileUserInfo((result) => {
    _currentProfileUser.value = result
  })

  // 设置监听（只会注册一次）
  chrome.identity.onSignInChanged.addListener((account) => {
    _currentProfileUser.value = account
  })

  isInitialized = true
}

export default function useProfileUser(): ProfileUserComposable {
  initializeProfileUser()

  return {
    currentProfileUser: computed(() => _currentProfileUser.value)
  };
}