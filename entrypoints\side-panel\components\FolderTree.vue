<script setup lang="ts">
import { shallowRef, watchEffect, ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { TreeRoot } from 'reka-ui'
import FolderTreeItem from './FolderTreeItem.vue'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import { monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { type Instruction, extractInstruction } from '@atlaskit/pragmatic-drag-and-drop-hitbox/tree-item'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import EditFolderDialog from './EditFolderDialog.vue'
import FolderBreadcrumbs from '@/components/FolderBreadcrumbs.vue'
import Icon from '@/components/Icon.vue'
import { Button } from '@/components/ui/button'
import { flattenBookmarks, findParentPath, openOrActivateManage } from '@/utils/bookmark'

const bookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const folders = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const expandedIds = ref<string[]>([])

const props = withDefaults(defineProps<{
  currentFolderId: string
  recentFolders: Array<{ id: string; name: string }>
}>(), {})

const emit = defineEmits<{
  newFolder: []
  folderSelected: [folderId: string]
}>()

const recentFoldersPopoverOpen = ref(false)

// 注入 setSelectedFolderId 方法
const setSelectedFolderId = inject<(folderId: string) => void>('setSelectedFolderId')

const isContextMenuOpen = ref(false);
const showEditFolderDialog = ref(false)

// 自动滚动到当前文件夹
const scrollAreaRef = ref<InstanceType<typeof ScrollArea>>();
const currentItemRef = ref<HTMLElement>();
const scrollToCurrentFolder = () => {
  nextTick(() => {
    if (!currentItemRef.value || !scrollAreaRef.value) return;
    
    const viewport = scrollAreaRef.value.$el.querySelector('[data-reka-scroll-area-viewport]');
    const targetElement = currentItemRef.value as unknown as HTMLElement;
    console.log('scrollToCurrentFolder', viewport, targetElement);

    if (viewport && targetElement) {
      console.log('scrollToCurrentFolder', viewport, targetElement);

      const targetTop = targetElement.offsetTop - viewport.offsetHeight / 3;
      viewport.scrollTo({
        top: targetTop,
        behavior: 'smooth'
      });
    }
  })
};

// 搜索
const searchQuery = ref(''); // 原始搜索关键词
const searchedFolders = ref<chrome.bookmarks.BookmarkTreeNode[]>([]) // 搜索结果
const searchedFolderIndex = ref(0);
const search = async () => {
  const flattenedFolders = flattenBookmarks(folders.value);
  searchedFolders.value = flattenedFolders.filter(folder => folder.title.toLowerCase().includes(searchQuery.value.toLowerCase()));
  searchedFolderIndex.value = 0;
}
// 将当前文件夹设置为搜索结果的第一个结果，每点击一次，改为下一个搜索结果 
const jumpToSearchFolder = () => {
  if (searchedFolders.value.length === 0) return;
  const searchedFolder = searchedFolders.value[searchedFolderIndex.value];
  setSelectedFolderId?.(searchedFolder.id);
  if (searchedFolderIndex.value < searchedFolders.value.length - 1) {
    searchedFolderIndex.value++;
  } else {
    searchedFolderIndex.value = 0;
  }
  scrollToCurrentFolder();
}
// 判断文件夹是否匹配搜索关键词
const isFolderMatched = (folder: any) => {
  if (!searchQuery.value) return true; // 如果防抖后的搜索关键词为空，所有文件夹都匹配
  const title = folder.raw?.title || folder.bind?.value?.title; // 从 folder 或 bind.value 中获取 title
  return title && title.toLowerCase().includes(searchQuery.value.toLowerCase());
};
watch(searchQuery, () => {
  if (searchQuery.value) {
    recentFoldersPopoverOpen.value = false;
  }
  search();
})

// 确保当前文件夹及其所有父级都展开
function ensureFolderExpanded(folderId: string) {
  const parentPath = findParentPath(folders.value, folderId);
  if (parentPath) {
    expandedIds.value = Array.from(new Set([...expandedIds.value, ...parentPath]));
  }
}

// 修改 watch currentFolderId 的监听器
watch(() => props.currentFolderId, async (newVal) => {
  ensureFolderExpanded(newVal);
  // await nextTick();
});

// 递归函数，过滤掉所有没有 children 的项目
function getFolders(bookmarks: chrome.bookmarks.BookmarkTreeNode[]) {
  return bookmarks.filter(bookmark => {
    if (bookmark.children) {
      bookmark.children = getFolders(bookmark.children); // 递归过滤子项
      return true;
    }
    return false;
  });
}

// 递归提取所有节点的 id
function extractIds(nodes: chrome.bookmarks.BookmarkTreeNode[]) {
  return nodes.map(node => {
    // 将当前节点的id添加到结果数组中
    const ids = [node.id];
    // 如果当前节点有子节点，递归提取子节点的id
    if (node.children) {
      ids.push(...extractIds(node.children));
    }
    return ids;
  }).flat();
}

function handleExpandedUpdate(ids: string[]) {
  expandedIds.value = ids;
}

watchEffect((onCleanup) => {
  const dndFunction = combine(
    monitorForElements({
      onDrop(args) {
        const { location, source } = args;

        // didn't drop on anything
        if (!location.current.dropTargets.length)
          return;

        const itemId = source.data.id as string;
        const target = location.current.dropTargets[0];
        const targetId = target.data.id as string;

        const instruction: Instruction | null = extractInstruction(
          target.data,
        );

        if (instruction !== null && instruction.type === 'make-child') {
          browser.bookmarks.move(itemId, { parentId: targetId });
        }
        if (instruction !== null && instruction.type === 'reorder-above') {
          const targetIndex = target.data.index as number;
          const parentId = target.data.parentId as string;
          browser.bookmarks.move(itemId, { parentId: parentId, index: targetIndex });
        }
        return;
        // if (instruction !== null) {
        //   items.value = updateTree(items.value, {
        //     type: 'instruction',
        //     instruction,
        //     itemId,
        //     targetId,
        //   }) ?? [];
        // }
      },
    }),
  );

  onCleanup(() => {
    dndFunction();
  });
});

async function setBookmarks() {
  const start = performance.now();
  bookmarks.value = await browser.bookmarks.getTree();
  folders.value = getFolders(bookmarks.value);
  expandedIds.value = extractIds(folders.value);
  const end = performance.now();
  console.log(`FolderTree getAllBookmark took ${(end - start).toFixed(0)} milliseconds`, bookmarks.value, folders.value);
}

async function handleDeleteFolder() {
  await browser.bookmarks.removeTree(props.currentFolderId);
}

async function handleClickEncryptFolder() {
  console.log('handleClickEncryptFolder', props.currentFolderId);
  setSelectedFolderId?.('encrypted');
}

// 打开书签管理器
function handleOpenManage() {
  openOrActivateManage(props.currentFolderId);
}

function handleRuntimeMessage(request: any) {
  console.warn('--------- popup FolderTree 收到消息 -----------!', request);
  if (request.type === 'bookmark-change') {
    setBookmarks();
  }
}

onMounted(async () => {
  setBookmarks();
  browser.runtime.onMessage.addListener(handleRuntimeMessage);
});

onBeforeUnmount(() => {
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
});

// 暴露方法给父组件
defineExpose({
  scrollToCurrentFolder
});
</script>

<template>
  <div class="flex flex-col justify-center w-full relative">
    <Popover v-model:open="recentFoldersPopoverOpen">
      <PopoverTrigger as-child>
        <!-- 搜索框 -->
        <div class="relative w-full items-center z-10">
          <Input
            v-model="searchQuery"
            :placeholder="i18n.t('popup.searchFolders')"
            class="rounded-none rounded-t-md pl-8 text-sm focus-visible:ring-0"
            @keydown.enter.prevent="jumpToSearchFolder()"
          />
          <span class="absolute start-0 inset-y-0 flex items-center justify-center px-2">
            <Icon name="lucide:search" class="size-4 text-muted-foreground" />
          </span>
          <Button 
            class="absolute end-0 inset-y-0 m-1 h-7"
            :class="{ 'hidden': !searchQuery }"
            type="button"
            :disabled="searchedFolders.length === 0"
            @click.prevent="jumpToSearchFolder()"
          >
            <Icon name="lucide:corner-down-left" class="size-3" /> 
            <span class="text-xs">{{ searchedFolders.length }}</span>
          </Button>
        </div>
      </PopoverTrigger>
    
      <PopoverContent class="p-1 w-(--reka-popover-trigger-width)">
        <ul>
          <li
            v-for="folder in props.recentFolders"
            :key="folder.id"
            @click="
              async () => {
                setSelectedFolderId?.(folder.id); // 调用注入的方法更新父组件的状态
                recentFoldersPopoverOpen = false; // 关闭 Popover
                scrollToCurrentFolder()
              }
            "
            class="cursor-pointer p-2 py-1 hover:bg-secondary"
          >
            <FolderBreadcrumbs :folderId="folder.id" :maxCrumbs="2" class="text-muted-foreground" />
          </li>
        </ul>
      </PopoverContent>
    </Popover>

    <ScrollArea 
      ref="scrollAreaRef"
      type="auto" 
      class="h-[300px] overflow-visible border border-t-0 rounded-none rounded-b-md shadow-[inset_0_0_6px_rgba(0,0,0,0.1)]"
    >
      <div class="flex flex-col gap-2 mx-2 py-1">
        <div 
          class="flex items-center gap-2 w-full hover:bg-muted z-0 cursor-default hover:text-primary" 
          :class="{ 
            'bg-muted font-semibold' : currentFolderId === 'encrypted'
          }"
          @click="handleClickEncryptFolder()"
        >
          <Icon name="lucide:folder" class="size-4" />
          <span class="text-sm text-muted-foreground">Protected Folders</span>
        </div>
      </div>
      <!-- 本地文件夹树 -->
      <TreeRoot
        v-slot="{ flattenItems }"
        class="w-full list-none select-none rounded-md text-sm font-normal"
        :items="folders[0]?.children"
        :get-key="(item) => item.id"
        multiple
        propagate-select
        :expanded="expandedIds"
        @update:expanded="handleExpandedUpdate"
      >
        <ContextMenu 
          v-model:open="isContextMenuOpen"
          @open-change="(open: boolean) => isContextMenuOpen = open"
        >
          <ContextMenuTrigger>
            <FolderTreeItem
              v-for="item in flattenItems"
              :key="item._id + item.index"
              :ref="el => { 
                if (item.value.id === currentFolderId) 
                  currentItemRef = (el as ComponentPublicInstance)?.$el || el as HTMLElement
              }"
              :item="item"
              v-bind="item.bind"
              :currentFolderId="currentFolderId"
              :class="[
                'flex items-center px-0 h-6 outline-hidden data-selected:bg-red-100',
                { 
                  'opacity-40': searchQuery && !isFolderMatched(item) 
                },
              ]"
              @select.prevent="() => { setSelectedFolderId?.(item.value.id) }"
              @contextmenu.native="() => { setSelectedFolderId?.(item.value.id) }"
            />
          </ContextMenuTrigger>
          <ContextMenuContent 
            class="v-context-menu-content"
          >
          <ContextMenuItem
              @select="emit('newFolder')"
            >
              {{ i18n.t('context_menu.newSubfolder') }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="showEditFolderDialog = !showEditFolderDialog"
            >
              {{ i18n.t('context_menu.rename') }}
            </ContextMenuItem>
            <ContextMenuItem
              @select="handleDeleteFolder()"
            >
              {{ i18n.t('context_menu.delete') }}
            </ContextMenuItem>
            <ContextMenuItem
              @select="handleOpenManage()"
            >
              <Icon name="lucide:share" class="mr-2 h-4 w-4" />
              {{ i18n.t('share.title') }}
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>
      </TreeRoot>
    </ScrollArea>

    <EditFolderDialog
      :open="showEditFolderDialog"
      :current-folder-id="props.currentFolderId"
      @open-change="(open: boolean) => (showEditFolderDialog = open)"
      @saved=""
    />
  </div>
</template>