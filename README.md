# WXT + Vue 3

This template should help get you started developing with Vue 3 in WXT.

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar).

1. add [tailwindcss](https://github.com/wxt-dev/examples/blob/main/examples/tailwindcss)

Shadcn 直接从自己维护的本地库中拷贝


关于扩展开发：
1. dateGroupModified 时间戳有可能为空，并且只有往文件夹直接新增书签的时候才会变化，删除和修改子书签都不会引起变化，往字文件夹写入也不会变化，修改文件夹名称也不会引起变化，因此应该用内容哈希进行文件夹变化比较，500 书签简单方法 (JSON.stringify + btoa): 1-3 毫秒
2. chrome.bookmark.get: 仅节点信息； chrome.bookmark.getChildren: 仅一级子节点信息；chrome.bookmark.getSubTree: 包括当前节点在内的所有子节点树