<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import {
  DialogContent,
  DialogOverlay,
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { watch, ref } from 'vue'

const props = defineProps<{
  open: boolean
  currentFolderId: string
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    title: z.string().min(1),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
})

const onSubmit = form.handleSubmit(async (values) => {
  try {
    const updatedFolder = await browser.bookmarks.update(props.currentFolderId, {
      title: values.title
    });
    emit('saved', updatedFolder)
    emit('openChange', false)
    toast.success(i18n.t('form.result.saved'))
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(i18n.t('error.unknown'))
  }
})

// 添加对对话框打开状态的监听
watch(() => props.open, async (isOpen) => {
  if (isOpen) {
    const results = await browser.bookmarks.get(props.currentFolderId)
    if (results[0]) {
      form.setValues({
        title: results[0].title
      })
    }
  } else {
    form.resetForm() // 关闭时重置表单
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
      <DialogContent
        class="fixed left-1/2 top-1/2 z-50 grid w-64 max-w-lg -translate-x-1/2 -translate-y-1/2 gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded sm:rounded-lg"
      >
        <DialogHeader class="hidden">
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <form
          id="editForm"
          class="mt-8 flex flex-col items-stretch gap-4"
          :validation-schema="itemSchema"
          @submit="onSubmit"
        >
          <FormField
            v-slot="{ componentField }"
            name="title"
          >
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="title"
                  v-bind="componentField"
                  :value="form.values.title"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        </form>

        <Button
          type="submit"
          form="editForm"
        >
          {{ i18n.t('dialog.action.save') }}
        </Button>
      </DialogContent>
  </Dialog>
</template>
