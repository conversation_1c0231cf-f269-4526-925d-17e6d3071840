import { initRxDB } from '@/rxdb';
const { currentUser, refreshSession } = useUserSession()
import { openSSE, closeSSE } from '@/utils/sse'
// import { setupRxDB } from './setupRxDB';
import { setupContextMenus } from './setupContextMenus';
import { setupActionIcon } from './setupActionIcon';
// import { setupSnapshot } from './setupSnapshot';

// 注意，background / bookmarks / popup 之间的上下文是隔离的
// 所以，如果需要共享 rxdb 实例，需要使用 chrome.runtime.onMessage 来传递消息
// 不做共享 rxdb 实例的方案，rxdb 允许多实例
// 也不做 crypto 后台共享实例的方案，因为是纯端对端加密，需要用户输入密码，做多上下文共享没有意义
export default defineBackground(() => {
  (async () => {
    try {
      // 初始化 rxdb, 获取或者创建一个 rxdb 实例
      await initRxDB();
      
      // 添加登录状态监听，watch 在这里作为副作用触发器，仍然是必要的
      watch(currentUser, async (newUser, oldUser) => {
        console.warn('background - currentUser changed - ', newUser, oldUser)
        // 当用户ID未变化时跳过
        if (newUser?.id === oldUser?.id) return;
        
        if (newUser) {
          await openSSE();
          // 初始化快照功能
          // setupSnapshot();
        } else {
          await closeSSE();
        }
      }, { immediate: true }); // 立即执行一次以处理初始状态

      refreshSession();
      const account = await browser.identity.getProfileUserInfo()
      console.warn('background -------- browser account', account)
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
    }
  })();

  // setupRxDB();
  setupContextMenus();
  setupActionIcon();
});

