<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { TagsInput, TagsInputInput, TagsInputItem, TagsInputItemDelete, TagsInputItemText } from '@/components/ui/tags-input'

const props = defineProps<{
  existTags: string[],  // 现有标签，可能会延迟计算
  allTags: string[],    // 所有可用标签
}>()

const emit = defineEmits(['tagsChange'])

// 内部状态，使用 props.existTags 作为初始值
const tags = ref<string[]>([...props.existTags])
const searchTerm = ref('')
const open = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)

// 当 existTags 变化时更新内部状态
watch(() => props.existTags, (newTags) => {
  tags.value = [...newTags]
})

// 过滤标签列表
const filteredTags = computed(() => {
  if (!searchTerm.value) return props.allTags
  return props.allTags.filter(tag => 
    tag.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})

// 当内部标签变化时通知父组件
const notifyTagsChange = (newTags: any[]) => {
  // 确保所有标签都是字符串
  const stringTags = newTags.map(tag => String(tag))
  tags.value = stringTags
  emit('tagsChange', stringTags)
  
  // 当标签变化时清空搜索词
  // searchTerm.value = ''
}

// 处理搜索输入
const handleInputChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  searchTerm.value = target.value
  inputRef.value = target
}

// 点击标签列表项
const handleSelect = (tag: string) => {
  const updatedTags = [...tags.value]
  
  if (updatedTags.includes(tag)) {
    const index = updatedTags.indexOf(tag)
    updatedTags.splice(index, 1)
  } else {
    updatedTags.push(tag)
  }
  
  notifyTagsChange(updatedTags)
  
  // 重新聚焦到输入框
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus()
    }
  })
}
</script>

<template>
  <Popover v-model:open="open" class="min-w-(--reka-select-trigger-width) w-(--reka-select-trigger-width)">
    <PopoverTrigger as-child>
      <div class="w-full" @click="open = true">
        <TagsInput
          v-model="tags"
          @update:model-value="notifyTagsChange"
          @focus="open = true"
          class="w-full px-2"
        >
          <TagsInputItem v-for="item in tags" :key="item" :value="item">
            <TagsInputItemText />
            <TagsInputItemDelete />
          </TagsInputItem>
          <TagsInputInput 
            placeholder="输入标签..." 
            @input="handleInputChange" 
          />
        </TagsInput>
      </div>
    </PopoverTrigger>

    <PopoverContent @open-auto-focus.prevent class="p-0 w-(--reka-popover-trigger-width)">
      <div class="p-2">
        <div v-if="filteredTags.length === 0" class="text-sm text-muted-foreground">No Exites Tags</div>
        <ul class="flex flex-wrap gap-2">
          <li
            v-for="tag in filteredTags"
            :key="tag"
            :class="[
              'bg-muted rounded px-2 py-1 cursor-pointer',
              tags.includes(tag) ? 'bg-primary text-primary-foreground' : ''
            ]"
            @click.prevent="handleSelect(tag)"
          >
            {{ tag }}
          </li>
        </ul>
      </div>
    </PopoverContent>
  </Popover>
</template> 