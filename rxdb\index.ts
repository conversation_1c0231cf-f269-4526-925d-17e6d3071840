import { createRxDatabase, RxJsonSchema, addRxPlugin, RxDatabase } from 'rxdb';
import { getRxStorageDexie } from 'rxdb/plugins/storage-dexie';
import { RxDBUpdatePlugin } from 'rxdb/plugins/update';
import { RxDBMigrationSchemaPlugin } from 'rxdb/plugins/migration-schema';
import { RxDBQueryBuilderPlugin } from 'rxdb/plugins/query-builder';
import { RxDBJsonDumpPlugin } from 'rxdb/plugins/json-dump';
addRxPlugin(RxDBJsonDumpPlugin);
// import { RxDBDevModePlugin } from 'rxdb/plugins/dev-mode';

// addRxPlugin(RxDBDevModePlugin);
addRxPlugin(RxDBQueryBuilderPlugin);
addRxPlugin(RxDBMigrationSchemaPlugin);
addRxPlugin(RxDBUpdatePlugin);
import cuid from 'cuid';

const collectionVersion = 1;
const currentBrowser = import.meta.env.BROWSER;
// 默认值只能使用常量不能使用函数，否则会导致 DB6 错误（Schema 不一致），也因此，主键 id 字段不能设置默认值
export const extraSchema: RxJsonSchema<any> = {
  title: 'Bookmark extra schema',
  description: 'The extra info with the bookmark',
  version: collectionVersion,
  primaryKey: "id",
  type: 'object',
  properties: {
    id: {
      type: 'string',
      maxLength: 50
    },
    userId: {
      type: 'string',
    },
    source: {
      type: 'string', // 来源：chrome / edge / safari / firefox / web
    },
    browserAccountId: {
      type: 'string',
    },
    bookmarkId: {
      type: 'string',
    },
    bookmarkType: {
      type: 'string', // 类型："browser" / "encrypted"
    },
    cover: {
      type: 'string',
    },
    description: {
      type: 'string',
    },
    note: {
      type: 'string',
    },
    emoji: {
      type: 'string',
    },
    iv: {
      type: 'string',
    },
    updatedAt: {
      type: 'string',
      format: "date-time"
    },
    createdAt: {
      type: 'string',
      format: "date-time"
    },
    deletedAt: {
      type: 'string',
      format: "date-time",
      default: null
    },
    // 不需要在 rxdb 表创建 deleted 字段，因为已指定 deletedField: 'deleted'， rxdb 会自动添加，服务端数据库则需要创建 deleted 字段以保存删除状态
    // deleted: {
    //   type: 'boolean',
    // },
    replicationRevision: {
      type: 'string',
      minLength: 3
    }
  }
};

export let rxDBInstance: RxDatabase;
export const dbName = 'gochat';
// 获取或者创建一个 rxdb 实例
export const getRxDBInstance = async () => {
  if (rxDBInstance) {
    return rxDBInstance;
  }
  return await initRxDB();
}
// 初始化 rxdb
export const initRxDB = async () => {
  console.warn('initRxDB called')
  if (rxDBInstance) {
    console.warn('-------- rxDBInstance already exists----------', rxDBInstance)
    return rxDBInstance;
  }
  
  try {
    rxDBInstance = await createRxDatabase({
      name: dbName,                   // <- name
      storage: getRxStorageDexie(),       // <- RxStorage

      /* Optional parameters: */
      // password: 'myPassword',             // <- password (optional)
      // multiInstance: true,                // <- multiInstance (optional, default: true)
      // eventReduce: true,                  // <- eventReduce (optional, default: false)
      cleanupPolicy: {},                   // <- custom cleanup policy (optional) 
    });
    
    // await rxDBInstance.addCollections({
    //   tags: {
    //     schema: tagSchema,
    //     migrationStrategies: {
    //       1: (oldDoc) => {
    //         console.log('Migrating tag:', oldDoc);
    //         return {
    //           ...oldDoc,
    //           source: 'chrome'
    //         };
    //       }
    //     },
    //   }
    // });

    // 添加集合
    const collections = await rxDBInstance.addCollections({
      extras: {
        schema: extraSchema,
        migrationStrategies: {
          1: function(oldDoc){
            return oldDoc;
          }
        }
      },
    });
    
    // 添加 preInsert 钩子以便设置默认值
    collections.extras.preInsert(async function(docData) {
      const { currentUser } = useUserSession()
      const browserAccount = await browser.identity.getProfileUserInfo();
      
      docData.id = docData.id ?? cuid();
      docData.userId = docData.userId ?? currentUser.value?.id ?? null;
      docData.source = docData.source ?? currentBrowser;
      docData.browserAccountId = browserAccount.id;
      docData.bookmarkType = docData.bookmarkType ?? "browser";
      docData.cover = docData.cover ?? null;
      docData.description = docData.description ?? null;
      docData.note = docData.note ?? null;
      docData.emoji = docData.emoji ?? null;
      docData.createdAt = docData.createdAt ?? new Date().toISOString();
      docData.updatedAt = docData.updatedAt ?? new Date().toISOString();
      return docData;
    }, false); // false 表示串行执行，true 表示并行执行

    return rxDBInstance;
  } catch (error) {
    console.error('initRxDB error', error);
  }
};

export default initRxDB;
